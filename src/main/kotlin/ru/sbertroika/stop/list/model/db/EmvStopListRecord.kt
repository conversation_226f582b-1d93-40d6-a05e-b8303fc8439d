package ru.sbertroika.stop.list.model.db

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.util.*

// разные бд
// env таблицы не используется в этом сервисе
@Table("emv_stop_list_record")
data class EmvStopListRecord(

    @Id
    @Column("id")
    var id: UUID? = null,

    @Column("project_id")
    var projectId: UUID? = null,

    @Column("created_at")
    var createdAt: Timestamp? = null,

    @Column("carrier_id")
    var carrierId: UUID? = null,

    @Column("uid")
    var uid: String? = null,

    @Column("hash")
    var hash: String? = null,

    @Column("hash_type")
    var hashType: Int? = null,

    @Column("version")
    var version: Long? = null,
)